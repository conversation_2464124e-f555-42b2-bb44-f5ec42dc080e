import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/components/panchang_item_always_change.dart';
import 'package:panchang_at_this_moment/components/panchang_item_sometime_change.dart';
import 'package:panchang_at_this_moment/components/static_panchang_item.dart';

class PanchangAtTheMoment extends StatefulWidget {
  final DateTime startTime;
  final double lat;
  final double lng;
  final double alt;
  const PanchangAtTheMoment({
    super.key,
    required this.startTime,
    required this.lat,
    required this.lng,
    required this.alt,
  });

  @override
  State<PanchangAtTheMoment> createState() => _PanchangAtTheMomentState();
}

class _PanchangAtTheMomentState extends State<PanchangAtTheMoment> {
  PanchangDataForThreeDays? _panchangData;
  PanchangDataForThreeDays? translatedPanchangData;
  bool _isPlaying = true;
  late DateTime _currentTime = widget.startTime;
  late Timer _timer;
  late String currentTranslatedLanguage = Localizations.localeOf(context).languageCode;

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_isPlaying) {
        setState(() {
          _currentTime = _currentTime.add(const Duration(seconds: 1));
        });
      }
    });
    _loadPanchangData();
  }

  Future<void> _loadPanchangData() async {
    final DateTime previousDay = widget.startTime.subtract(const Duration(days: 1));
    final DateTime nextDay = widget.startTime.add(const Duration(days: 1));

    final List<Future<PanchangData>> futures = [
      PanchangAPI.getPanchangData(previousDay, widget.lat, widget.lng, widget.alt),
      PanchangAPI.getPanchangData(widget.startTime, widget.lat, widget.lng, widget.alt),
      PanchangAPI.getPanchangData(nextDay, widget.lat, widget.lng, widget.alt),
    ];

    final List<PanchangData> data = await Future.wait(futures);

    setState(() {
      _panchangData = PanchangDataForThreeDays(
        previousDay: data[0],
        currentDay: data[1],
        nextDay: data[2],
      );
      translatedPanchangData = PanchangDataForThreeDays(
        previousDay: _panchangData!.previousDay.translate(Localizations.localeOf(context).languageCode),
        currentDay: _panchangData!.currentDay.translate(Localizations.localeOf(context).languageCode),
        nextDay: _panchangData!.nextDay.translate(Localizations.localeOf(context).languageCode),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return panchangInfoBox(context, _currentTime);
  }

  Widget panchangInfoBox(BuildContext context, DateTime currentTime) {
    final TextStyle titleTextStyle = Theme.of(context).textTheme.titleLarge!;
    final TextStyle smallTextStyle = Theme.of(context).textTheme.bodySmall!;
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth > 1024;
    final isTablet = screenWidth > 600 && screenWidth <= 1024;
    final isMobile = screenWidth <= 600;
    
    // translation part:
    if (_panchangData != null && currentTranslatedLanguage != Localizations.localeOf(context).languageCode) {
      translatedPanchangData = PanchangDataForThreeDays(
        previousDay: _panchangData!.previousDay.translate(Localizations.localeOf(context).languageCode), 
        currentDay: _panchangData!.currentDay.translate(Localizations.localeOf(context).languageCode), 
        nextDay: _panchangData!.nextDay.translate(Localizations.localeOf(context).languageCode)
      );
      currentTranslatedLanguage = Localizations.localeOf(context).languageCode;
    }

    final Widget? panchangSomeTimeChangeBox = (translatedPanchangData != null)
        ? getPanchangItemSomeTimeChange(
            panchangData: translatedPanchangData!,
            currentTime: currentTime,
            titleTextStyle: titleTextStyle,
            smallTextStyle: smallTextStyle,
            context: context)
        : null;

    if (translatedPanchangData == null) {
      return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFFF3E5AB), // Warm cream color
              Colors.white,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  color: Color(0xFF6B4E3D),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                "Loading Panchang data...",
                style: GoogleFonts.mallanna(
                  color: const Color(0xFF5D4037),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Get all items to display
    final staticItems = getStaticPanchangItem(
      translatedPanchangData,
      DateTime(_currentTime.year, _currentTime.month, _currentTime.day),
      context,
    );
    
    final dynamicItems = getPanchangItemAlwayChange(
      panchangData: translatedPanchangData,
      context: context,
      currentTime: currentTime,
    );
    
    // Organize items by priority and grouping
    // Priority 1: Time-based and frequently checked items
    final priorityItems = [
      ...staticItems, // Sun/Moon times and weekday
    ];

    // Priority 2: Current astrological states (most important dynamic items)
    final currentStateItems = [
      if (dynamicItems.isNotEmpty) dynamicItems[0], // Tithi Paksha
    ];

    // Priority 3: Other astrological elements
    final otherItems = [
      if (dynamicItems.length > 1) dynamicItems[1], // Karana
      if (dynamicItems.length > 2) dynamicItems[2], // Yoga
      if (dynamicItems.length > 3) dynamicItems[3], // Maasa
      if (dynamicItems.length > 4) dynamicItems[4], // Samvatsara
      if (dynamicItems.length > 5) dynamicItems[5], // Nakshatra (combined card)
      if (dynamicItems.length > 6) dynamicItems[6], // Surya Nakshatra
    ];

    // Combine all items with priority ordering
    final allItems = [...priorityItems, ...currentStateItems, ...otherItems];



    // Main content with responsive grid
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFF3E5AB), // Warm cream color
            Colors.white,
          ],
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 2.0 : 12.0),
        child: Column(
          children: [
            // Time control widget - enhanced styling
            Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.white, Color(0xFFFAFAFA)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 3),
                  ),
                ],
                border: Border.all(color: const Color(0xFFE0E0E0), width: 0.5),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF3E5AB),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.access_time_rounded,
                          color: Color(0xFF6B4E3D),
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        DateFormat('dd-MM-yyyy HH:mm:ss').format(currentTime),
                        style: Theme.of(context).textTheme.displayMedium?.copyWith(
                          color: const Color(0xFF3E2723),
                          fontWeight: FontWeight.w600,
                          fontSize: isMobile ? 14 : (isTablet ? 16 : 18),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: _isPlaying ? const Color(0xFFFFEBEE) : const Color(0xFFE8F5E8),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                          constraints: const BoxConstraints(minWidth: 44, minHeight: 44),
                          padding: const EdgeInsets.all(8),
                          onPressed: () {
                            setState(() {
                              _isPlaying = !_isPlaying;
                              _currentTime = currentTime;
                            });
                          },
                          icon: Icon(
                            _isPlaying
                                ? Icons.pause_circle_filled_rounded
                                : Icons.play_circle_fill_rounded,
                            color: _isPlaying ? const Color(0xFFD32F2F) : const Color(0xFF388E3C),
                            size: 24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFF3E5AB),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                          constraints: const BoxConstraints(minWidth: 44, minHeight: 44),
                          padding: const EdgeInsets.all(8),
                          icon: const Icon(
                            Icons.refresh_rounded,
                            color: Color(0xFF6B4E3D),
                            size: 22,
                          ),
                          onPressed: () {
                            setState(() {
                              _currentTime = DateTime.now();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Main content - no scrolling, fixed height
            Expanded(
              child: Column(
                children: [
                  // Main Muhurta card (only if available) - enhanced styling
                  if (panchangSomeTimeChangeBox != null)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colors.white, Color(0xFFFFFBF0)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.08),
                            blurRadius: 10,
                            spreadRadius: 0,
                            offset: const Offset(0, 3),
                          ),
                        ],
                        border: Border.all(color: const Color(0xFFE8E8E8), width: 0.5),
                      ),
                      child: panchangSomeTimeChangeBox,
                    ),

                  // Responsive grid of cards - takes remaining space
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          // Determine the number of cards per row based on screen width and orientation
                          final orientation = MediaQuery.of(context).orientation;
                          int crossAxisCount;
                          double minCardWidth = 160; // Minimum card width for readability

                          if (isDesktop) {
                            crossAxisCount = orientation == Orientation.landscape ? 5 : 4;
                          } else if (isTablet) {
                            crossAxisCount = orientation == Orientation.landscape ? 4 : 3;
                          } else {
                            crossAxisCount = orientation == Orientation.landscape ? 3 : 2;
                          }

                          // Ensure cards don't get too small
                          double calculatedWidth = (constraints.maxWidth / crossAxisCount) - 12;
                          if (calculatedWidth < minCardWidth && crossAxisCount > 2) {
                            crossAxisCount = (constraints.maxWidth / (minCardWidth + 12)).floor().clamp(2, crossAxisCount);
                          }

                          // Calculate item size with improved spacing
                          double itemWidth = (constraints.maxWidth / crossAxisCount) - 12;

                          return SingleChildScrollView(
                            child: Wrap(
                              spacing: isMobile ? 6.0 : 10.0,
                              runSpacing: isMobile ? 8.0 : 12.0,
                              alignment: WrapAlignment.center,
                              children: allItems.map((item) {
                                return Container(
                                  width: itemWidth,
                                  padding: const EdgeInsets.symmetric(vertical: 2,horizontal: 3),
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [Colors.white, Color(0xFFFFFDF7)],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.08),
                                        blurRadius: 8,
                                        spreadRadius: 0,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                    border: Border.all(color: const Color(0xFFE8E8E8), width: 0.5),
                                  ),
                                  child: item,
                                );
                              }).toList(),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}